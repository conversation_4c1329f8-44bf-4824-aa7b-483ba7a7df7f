// ============================================================================
// UNIVERSAL AI BRAIN - TYPE DEFINITIONS
// ============================================================================

export interface UniversalAIBrainConfig {
  mongodb: {
    connectionString: string;
    databaseName: string;
    collections?: {
      memory?: string;
      conversations?: string;
      context?: string;
      improvements?: string;
      traces?: string;
      metrics?: string;
    };
  };
  embeddings: {
    provider: 'voyage-ai' | 'openai';
    voyageAI?: {
      apiKey: string;
      model?: string;
      outputDimension?: number;
    };
    openAI?: {
      apiKey: string;
      model?: string;
      baseUrl?: string;
    };
  };
  brain?: {
    enableSemanticMemory?: boolean;
    enableContextInjection?: boolean;
    enableSelfImprovement?: boolean;
    enableSafetyGuardrails?: boolean;
  };
}

export interface Memory {
  id: string;
  content: string;
  type: string;
  userId?: string;
  relevanceScore?: number;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export interface SearchQuery {
  query: string;
  userId?: string;
  limit?: number;
  filter?: Record<string, any>;
}

export interface GenerationInput {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  userId?: string;
  options?: Record<string, any>;
}

export interface GenerationOutput {
  text: string;
  memories?: Memory[];
  enhancement?: string;
  metadata?: Record<string, any>;
}

export declare class UniversalAIBrain {
  constructor(config: UniversalAIBrainConfig);
  
  initialize(): Promise<UniversalAIBrain>;
  storeMemory(memory: Partial<Memory>): Promise<string>;
  semanticSearch(query: SearchQuery): Promise<Memory[]>;
  generateWithMemory(input: GenerationInput): Promise<GenerationOutput>;
  enhance<T>(framework: T): T & { _universalAIBrain: UniversalAIBrain; _enhanced: true; _intelligenceBoost: string };
}

export declare class MastraAdapter {
  constructor();
  integrate(brain: UniversalAIBrain): any;
}

export declare class VercelAIAdapter {
  constructor();
  integrate(brain: UniversalAIBrain): any;
}

export declare class LangChainJSAdapter {
  constructor();
  integrate(brain: UniversalAIBrain): any;
}

export declare class OpenAIAgentsAdapter {
  constructor();
  integrate(brain: UniversalAIBrain): any;
}

export declare function createUniversalAIBrain(config: UniversalAIBrainConfig): UniversalAIBrain;
export declare function getVersion(): string;
export declare function showWelcomeMessage(): void;

export default UniversalAIBrain;
