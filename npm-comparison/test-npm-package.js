// Test the npm package with real MongoDB connection
import { UniversalAIBrain, createUniversalAIBrain } from './package/index.js';

async function testNpmPackage() {
  console.log('🧪 Testing npm package with real MongoDB connection...\n');
  
  try {
    // Create brain instance with your MongoDB connection
    const brain = createUniversalAIBrain({
      mongodb: {
        connectionString: 'mongodb+srv://romiluz:<EMAIL>/?retryWrites=true&w=majority&appName=agents',
        databaseName: 'ai_brain_test'
      },
      embeddings: {
        provider: 'openai',
        openAI: {
          apiKey: '********************************************************************************************************************************************************************'
        }
      }
    });

    console.log('✅ Brain instance created');

    // Initialize the brain
    await brain.initialize();
    console.log('✅ Brain initialized');

    // Test memory storage
    console.log('\n📝 Testing memory storage...');
    const memoryId = await brain.storeMemory({
      content: 'User loves Italian food and prefers vegetarian options',
      type: 'preference',
      userId: 'test-user-123'
    });
    console.log(`✅ Memory stored with ID: ${memoryId}`);

    // Test semantic search
    console.log('\n🔍 Testing semantic search...');
    const searchResults = await brain.semanticSearch({
      query: 'What food does the user like?',
      userId: 'test-user-123'
    });
    console.log('✅ Search results:', JSON.stringify(searchResults, null, 2));

    // Test memory-enhanced generation
    console.log('\n🧠 Testing memory-enhanced generation...');
    const response = await brain.generateWithMemory({
      messages: [
        { role: 'user', content: 'Recommend me a restaurant' }
      ],
      userId: 'test-user-123'
    });
    console.log('✅ Generated response:', JSON.stringify(response, null, 2));

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 CONCLUSION: This appears to be a REAL implementation, not a mock!');

  } catch (error) {
    console.error('❌ Error testing npm package:', error);
    
    if (error.message.includes('MongoDB') || error.message.includes('connection')) {
      console.log('\n💡 This suggests it\'s trying to make real MongoDB connections!');
    }
    
    if (error.message.includes('OpenAI') || error.message.includes('API')) {
      console.log('\n💡 This suggests it\'s trying to make real OpenAI API calls!');
    }
  }
}

// Run the test
testNpmPackage().catch(console.error);
