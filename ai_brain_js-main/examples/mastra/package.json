{"name": "universal-ai-brain-mastra-demo", "version": "1.0.0", "description": "Real-world demonstration of Universal AI Brain enhancing Mastra framework", "main": "chef-with-universal-brain.ts", "scripts": {"demo": "npx tsx chef-with-universal-brain.ts", "build": "tsc", "dev": "npx tsx --watch chef-with-universal-brain.ts"}, "dependencies": {"@mastra/core": "latest", "@ai-sdk/openai": "^0.0.66", "mongodb": "^6.10.0", "openai": "^4.67.3", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.0.0", "tsx": "^4.19.2", "typescript": "^5.6.3"}, "keywords": ["universal-ai-brain", "mastra", "mongodb", "vector-search", "ai-agents", "semantic-memory"], "author": "Universal AI Brain", "license": "MIT"}