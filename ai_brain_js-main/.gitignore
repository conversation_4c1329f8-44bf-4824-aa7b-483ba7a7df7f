# Universal AI Brain - Production .gitignore
# This ensures only the essential files are included in the open source release

# ============================================================================
# NODE.JS & NPM
# ============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# ============================================================================
# ENVIRONMENT & SECRETS
# ============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.chef-test

# MongoDB connection strings and API keys
.env.production
.env.staging
.env.development

# ============================================================================
# BUILD OUTPUTS
# ============================================================================
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/

# Turbo
.turbo/

# ============================================================================
# TESTING & DEVELOPMENT
# ============================================================================
# Jest
coverage/
.jest/

# Testing artifacts
test-results/
test-output/
*.test.log

# Development test files (keep these out of open source)
*-test.js
*-demo.js
test-*.js
simple-*.js
temp-*.js
working-*.js
focused-*.js
comprehensive-*.js

# Chef Michel test files (internal development)
chef-michel-*.js
run-chef-test.js
mastra-universal-brain-demo.js

# ============================================================================
# INTERNAL DOCUMENTATION & PLANNING (KEEP PRIVATE)
# ============================================================================
# Internal planning documents
CLEANUP_SUMMARY.md
TEST_RESULTS_SUMMARY.md
CHEF-MICHEL-TEST.md
FINAL-RELEASE-SUMMARY.md
FINAL-READINESS-REPORT.md
SOCIAL-MEDIA-KIT.md
RELEASE-NOTES.md

# Internal development notes
internal-docs/
planning/
notes/
brainstorming/
strategy/

# Generated test directories
universal-ai-brain-all-frameworks/

# ============================================================================
# MONGODB & DATABASE
# ============================================================================
# MongoDB data directories
data/
db/
mongodb-data/

# Database dumps
*.dump
*.bson
*.json.gz

# ============================================================================
# LOGS & TEMPORARY FILES
# ============================================================================
logs/
*.log

# Temporary folders
tmp/
temp/
.tmp/

# ============================================================================
# OPERATING SYSTEM
# ============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Windows
Thumbs.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ============================================================================
# EDITORS & IDEs
# ============================================================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.swp
*.swo

# ============================================================================
# SECURITY & CERTIFICATES
# ============================================================================
# SSL certificates
*.pem
*.key
*.crt

# SSH keys
id_rsa
id_rsa.pub

# ============================================================================
# MISC
# ============================================================================
# Backup files
*.bak
*.backup
*.old
*.orig
