{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Traces", "description": "Schema for the 'traces' collection", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "trace_id": {"type": "string"}, "workflow_id": {"type": "string"}, "plan_id": {"type": "string"}, "agent_id": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "trace_data": {"type": "array", "items": {"type": "object"}}}, "required": ["trace_id", "agent_id", "timestamp", "trace_data"]}