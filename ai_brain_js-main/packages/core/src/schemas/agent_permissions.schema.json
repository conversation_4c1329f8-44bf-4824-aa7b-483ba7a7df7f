{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent Permissions", "description": "Schema for the 'agent_permissions' collection", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "agent_id": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"tool_id": {"type": "string"}, "policy": {"type": "string", "enum": ["always_allow", "always_deny", "requires_human_approval"]}, "approver_group": {"type": "string"}}, "required": ["tool_id", "policy"]}}}, "required": ["agent_id", "permissions"]}