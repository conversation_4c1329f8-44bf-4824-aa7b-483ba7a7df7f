/**
 * UNIVERSAL AI BRAIN - PRODUCTION-READY MONGODB SCHEMAS
 * 
 * This file defines the PERFECT data architecture for Universal AI Brain.
 * Every collection, field, and index is designed for:
 * - Crystal clear organization
 * - Production performance
 * - Developer understanding
 * - Industry-standard practices
 * 
 * NAMING CONVENTION: uab_ prefix (Universal AI Brain)
 * PRINCIPLE: Every schema should be immediately understandable
 */

import { z } from 'zod';

// ============================================================================
// CORE DATA TYPES
// ============================================================================

export const FrameworkType = z.enum(['mastra', 'vercel-ai', 'langchain', 'openai-agents', 'custom']);
export const MemoryType = z.enum(['conversation', 'preference', 'fact', 'skill', 'context', 'feedback']);
export const SessionStatus = z.enum(['active', 'paused', 'completed', 'archived']);
export const TraceLevel = z.enum(['debug', 'info', 'warn', 'error']);

// ============================================================================
// UAB_MEMORIES - The Heart of Universal AI Brain
// ============================================================================

export const UABMemorySchema = z.object({
  _id: z.string().optional(), // MongoDB ObjectId
  
  // Core Identification
  userId: z.string().describe('User identifier - indexed for fast lookup'),
  sessionId: z.string().describe('Session identifier - groups related memories'),
  agentId: z.string().describe('Agent that created this memory'),
  
  // Memory Content
  content: z.string().describe('The actual memory content - searchable text'),
  embedding: z.array(z.number()).optional().describe('Vector embedding for semantic search'),
  
  // Memory Classification
  type: MemoryType.describe('Type of memory for organization'),
  importance: z.number().min(1).max(10).describe('Relevance score 1-10'),
  
  // Framework Integration
  framework: FrameworkType.describe('AI framework that created this memory'),
  
  // Metadata
  metadata: z.object({
    timestamp: z.date().describe('When this memory was created'),
    tags: z.array(z.string()).describe('Searchable tags'),
    source: z.string().describe('Where this memory came from'),
    confidence: z.number().min(0).max(1).describe('Confidence in memory accuracy'),
    language: z.string().default('en').describe('Language of the content')
  }),
  
  // Relationships
  relationships: z.array(z.string()).describe('ObjectIds of related memories'),
  parentMemoryId: z.string().optional().describe('Parent memory if this is a follow-up'),
  
  // Lifecycle
  createdAt: z.date().describe('Creation timestamp - indexed'),
  updatedAt: z.date().describe('Last update timestamp'),
  expiresAt: z.date().optional().describe('Auto-deletion date for temporary memories')
});

// ============================================================================
// UAB_SESSIONS - Conversation Organization
// ============================================================================

export const UABSessionSchema = z.object({
  _id: z.string().optional(),
  
  // Core Identification
  userId: z.string().describe('User identifier - indexed'),
  agentId: z.string().describe('Agent identifier - indexed'),
  
  // Session Details
  sessionName: z.string().describe('Human-readable session name'),
  description: z.string().optional().describe('Session description or purpose'),
  
  // Timing
  startTime: z.date().describe('Session start time'),
  lastActivity: z.date().describe('Last interaction time - indexed for cleanup'),
  endTime: z.date().optional().describe('Session end time'),
  
  // Statistics
  messageCount: z.number().default(0).describe('Total messages in session'),
  memoryCount: z.number().default(0).describe('Memories created in session'),
  
  // Status
  status: SessionStatus.describe('Current session status'),
  
  // Framework Integration
  framework: FrameworkType.describe('Framework used in this session'),
  
  // Metadata
  metadata: z.object({
    userAgent: z.string().optional().describe('User agent string'),
    ipAddress: z.string().optional().describe('User IP (hashed for privacy)'),
    location: z.string().optional().describe('User location (if available)'),
    deviceType: z.string().optional().describe('Device type (mobile, desktop, etc.)'),
    customData: z.record(z.any()).optional().describe('Framework-specific data')
  }),
  
  // Lifecycle
  createdAt: z.date(),
  updatedAt: z.date()
});

// ============================================================================
// UAB_AGENTS - Agent Configuration and Metadata
// ============================================================================

export const UABAgentSchema = z.object({
  _id: z.string().optional(),
  
  // Agent Identity
  agentId: z.string().describe('Unique agent identifier'),
  name: z.string().describe('Human-readable agent name'),
  description: z.string().describe('Agent purpose and capabilities'),
  
  // Configuration
  instructions: z.string().describe('Agent instructions/system prompt'),
  model: z.string().describe('LLM model being used'),
  framework: FrameworkType.describe('AI framework'),
  
  // Capabilities
  capabilities: z.object({
    hasMemory: z.boolean().default(true).describe('Can store and recall memories'),
    hasLearning: z.boolean().default(true).describe('Can improve over time'),
    hasContext: z.boolean().default(true).describe('Uses context injection'),
    hasTools: z.boolean().default(false).describe('Has access to tools'),
    hasVoice: z.boolean().default(false).describe('Has voice capabilities')
  }),
  
  // Performance Metrics
  metrics: z.object({
    totalSessions: z.number().default(0),
    totalMemories: z.number().default(0),
    averageResponseTime: z.number().default(0),
    satisfactionScore: z.number().default(0),
    lastUsed: z.date().optional()
  }),
  
  // Lifecycle
  createdAt: z.date(),
  updatedAt: z.date(),
  isActive: z.boolean().default(true)
});

// ============================================================================
// UAB_TRACES - Execution Traces and Debugging
// ============================================================================

export const UABTraceSchema = z.object({
  _id: z.string().optional(),
  
  // Trace Identity
  traceId: z.string().describe('Unique trace identifier'),
  parentTraceId: z.string().optional().describe('Parent trace for nested operations'),
  
  // Context
  userId: z.string().describe('User identifier'),
  sessionId: z.string().describe('Session identifier'),
  agentId: z.string().describe('Agent identifier'),
  
  // Execution Details
  operation: z.string().describe('Operation being traced'),
  framework: FrameworkType.describe('Framework used'),
  
  // Timing
  startTime: z.date().describe('Operation start time'),
  endTime: z.date().optional().describe('Operation end time'),
  duration: z.number().optional().describe('Duration in milliseconds'),
  
  // Status
  status: z.enum(['started', 'completed', 'failed', 'timeout']),
  level: TraceLevel.describe('Log level'),
  
  // Data
  input: z.record(z.any()).optional().describe('Input data'),
  output: z.record(z.any()).optional().describe('Output data'),
  error: z.string().optional().describe('Error message if failed'),
  
  // Performance
  metrics: z.object({
    memoriesRetrieved: z.number().default(0),
    contextInjected: z.boolean().default(false),
    tokensUsed: z.number().default(0),
    cost: z.number().default(0)
  }),
  
  // Lifecycle
  createdAt: z.date(),
  expiresAt: z.date().describe('Auto-deletion date (30 days default)')
});

// ============================================================================
// UAB_IMPROVEMENTS - Self-Improvement Tracking
// ============================================================================

export const UABImprovementSchema = z.object({
  _id: z.string().optional(),
  
  // Context
  userId: z.string().describe('User identifier'),
  agentId: z.string().describe('Agent identifier'),
  sessionId: z.string().optional().describe('Session where improvement occurred'),
  
  // Improvement Details
  type: z.enum(['feedback', 'performance', 'accuracy', 'relevance', 'speed']),
  description: z.string().describe('What was improved'),
  
  // Metrics
  beforeScore: z.number().describe('Score before improvement'),
  afterScore: z.number().describe('Score after improvement'),
  improvement: z.number().describe('Improvement amount'),
  
  // Implementation
  action: z.string().describe('What action was taken'),
  framework: FrameworkType.describe('Framework used'),
  
  // Validation
  validated: z.boolean().default(false).describe('Has this improvement been validated'),
  validationScore: z.number().optional().describe('Validation score'),
  
  // Lifecycle
  createdAt: z.date(),
  appliedAt: z.date().optional().describe('When improvement was applied')
});

// ============================================================================
// COLLECTION NAMES - Consistent Naming Convention
// ============================================================================

export const UAB_COLLECTIONS = {
  MEMORIES: 'uab_memories',
  SESSIONS: 'uab_sessions', 
  AGENTS: 'uab_agents',
  TRACES: 'uab_traces',
  IMPROVEMENTS: 'uab_improvements',
  METRICS: 'uab_metrics',
  EMBEDDINGS: 'uab_embeddings'
} as const;

// ============================================================================
// INDEX DEFINITIONS - Production Performance
// ============================================================================

export const UAB_INDEXES = {
  // uab_memories indexes
  MEMORIES: [
    { fields: { userId: 1, sessionId: 1, createdAt: -1 }, name: 'user_session_time' },
    { fields: { userId: 1, type: 1, importance: -1 }, name: 'user_type_importance' },
    { fields: { agentId: 1, createdAt: -1 }, name: 'agent_time' },
    { fields: { content: 'text' }, name: 'content_text_search' },
    { fields: { 'metadata.tags': 1 }, name: 'tags_search' },
    { fields: { expiresAt: 1 }, name: 'auto_cleanup', expireAfterSeconds: 0 }
  ],
  
  // uab_sessions indexes
  SESSIONS: [
    { fields: { userId: 1, lastActivity: -1 }, name: 'user_activity' },
    { fields: { agentId: 1, status: 1 }, name: 'agent_status' },
    { fields: { framework: 1, createdAt: -1 }, name: 'framework_time' }
  ],
  
  // uab_traces indexes
  TRACES: [
    { fields: { agentId: 1, startTime: -1 }, name: 'agent_time' },
    { fields: { sessionId: 1, operation: 1 }, name: 'session_operation' },
    { fields: { expiresAt: 1 }, name: 'auto_cleanup', expireAfterSeconds: 0 }
  ]
};

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type UABMemory = z.infer<typeof UABMemorySchema>;
export type UABSession = z.infer<typeof UABSessionSchema>;
export type UABAgent = z.infer<typeof UABAgentSchema>;
export type UABTrace = z.infer<typeof UABTraceSchema>;
export type UABImprovement = z.infer<typeof UABImprovementSchema>;
